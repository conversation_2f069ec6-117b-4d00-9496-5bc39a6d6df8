<script setup lang="ts">
import type { Dayjs } from 'dayjs';

import { computed } from 'vue';

import { RangePicker } from 'ant-design-vue';
import dayjs from 'dayjs';

// 定义支持的日期范围类型，兼容 Ant Design Vue RangePicker 的类型
type DateRange = [Dayjs, Dayjs] | [string, string] | undefined;

interface Props {
  value?: DateRange;
  modelValue?: DateRange;
}

interface Emits {
  (e: 'update:value', value: [Dayjs, Dayjs] | null): void;
  (e: 'update:modelValue', value: [Dayjs, Dayjs] | null): void;
  (e: 'change', value: [Dayjs, Dayjs] | null): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 处理日期范围变化，自动设置时间为 00:00:00 到 23:59:59
const handleChange = (value: [Dayjs, Dayjs] | [string, string] | null | undefined, _dateString: [string, string]) => {
  let processedDates: [Dayjs, Dayjs] | null = null;

  if (value && Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
    // 确保转换为 Dayjs 对象
    const startDate = dayjs.isDayjs(value[0]) ? value[0] : dayjs(value[0]);
    const endDate = dayjs.isDayjs(value[1]) ? value[1] : dayjs(value[1]);

    if (startDate.isValid() && endDate.isValid()) {
      // 将开始时间设置为当天的 00:00:00，结束时间设置为当天的 23:59:59
      const adjustedStartDate = startDate.hour(0).minute(0).second(0).millisecond(0);
      const adjustedEndDate = endDate.hour(23).minute(59).second(59).millisecond(999);
      processedDates = [adjustedStartDate, adjustedEndDate];
    }
  }

  // 发出所有相关事件
  emit('update:value', processedDates);
  emit('update:modelValue', processedDates);
  emit('change', processedDates);
};

// 计算当前值，优先使用 modelValue，然后是 value
const currentValue = computed(() => {
  return props.modelValue ?? props.value;
});
</script>
<template>
  <RangePicker :value="currentValue" v-bind="$attrs" @change="handleChange" />
</template>
