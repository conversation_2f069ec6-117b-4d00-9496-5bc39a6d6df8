<script setup lang="ts">
import type { CascaderProps } from 'ant-design-vue';
import type { DefaultOptionType } from 'ant-design-vue/es/vc-cascader';

import type { RegionInfo } from '@vben/types';

import { computed, ref, watchEffect } from 'vue';

import { Cascader } from 'ant-design-vue';
import { find } from 'lodash-es';

const props = defineProps({
  reginApi: { type: Function, required: true },
});

const province = defineModel('province', { type: [String, null] });
const city = defineModel('city', { type: [String, null] });
const district = defineModel('district', { type: [String, null] });
const districtCode = defineModel('districtCode', { type: [String, null] });

const value = computed({
  get() {
    const arr = [];
    if (province.value) arr[0] = province.value;
    if (city.value) arr[1] = city.value;
    if (district.value) arr[2] = district.value;
    return arr;
  },
  set(val: string[]) {
    province.value = val[0];
    city.value = val[1];
    district.value = val[2];
  },
});

const options = ref<any[]>([]);
const isReplayDone = ref(false);
const fieldNames = {
  label: 'name',
  value: 'name',
};

const stopWatch = watchEffect(async () => {
  if (value.value.length > 0 && options.value.length > 0 && !isReplayDone.value) {
    isReplayDone.value = true;
    let currentOptions: RegionInfo = {};
    if (value.value[0]) {
      currentOptions = find(options.value, { name: value.value[0] }) as RegionInfo;
    }
    if (value.value[1]) {
      const cityList = await props.reginApi({ parentId: currentOptions.id });
      cityList.forEach((item: RegionInfo) => {
        item.isLeaf = false;
      });
      const city = find(cityList, { name: value.value[1] }) as RegionInfo;
      if (value.value[2]) {
        city.children = await props.reginApi({ parentId: city.id });
      }
      currentOptions.children = cityList;
    }
    options.value = [...options.value];
    stopWatch();
  }
});

const loadData: CascaderProps['loadData'] = async (selectedOptions) => {
  isReplayDone.value = true;
  const targetOption = selectedOptions[selectedOptions.length - 1];
  if (targetOption) {
    targetOption.loading = true;
    const res = await props.reginApi({ parentId: targetOption.id });
    res.forEach((item: RegionInfo) => {
      if (item.levelType !== '3') {
        item.isLeaf = false;
      }
    });
    targetOption.children = res;
    options.value = [...options.value];
    targetOption.loading = false;
  }
};

const changeRegion = (_val: string[], selectedOptions: DefaultOptionType[] | DefaultOptionType[][]) => {
  const last = selectedOptions[selectedOptions.length - 1] as RegionInfo;
  if (last) {
    districtCode.value = last.id;
  }
};

const init = async () => {
  const res = await props.reginApi({ parentId: '100000' });
  res.forEach((item: { children: RegionInfo[]; isLeaf: boolean }) => {
    item.isLeaf = false;
  });
  options.value = res;
};

init();
</script>

<template>
  <Cascader
    v-model:value="value"
    :options="options"
    :load-data="loadData"
    :field-names="fieldNames"
    change-on-select
    @change="changeRegion"
  />
</template>

<style scoped></style>
