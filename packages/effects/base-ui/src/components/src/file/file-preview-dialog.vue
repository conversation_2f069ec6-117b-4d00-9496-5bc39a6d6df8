<script setup lang="ts">
import { ref } from 'vue';

import { Modal } from 'ant-design-vue';

import FilePreview from './file-preview.vue';

const props = defineProps({
  width: {
    type: String,
    default: '1200px',
  },
  height: {
    type: String,
    default: '600px',
  },
  previewApi: {
    type: Function,
    required: true,
  },
  downloadApi: {
    type: Function,
    default: null,
  },
});
const id = ref<number>();
const visible = ref(false);
const init = async (fileId: number) => {
  id.value = fileId;
  try {
    const body = await props.previewApi({ id: fileId }, { responseReturn: 'body' });
    if (body.code !== 200) {
      throw new Error(body.msg);
    }
    visible.value = true;
  } catch (error) {
    console.warn('预览失败，尝试下载', error);
    const res = await props.downloadApi({ id: fileId });
    window.open(res, '_blank');
  }
};
defineExpose({
  init,
});
</script>

<template>
  <Modal v-model:open="visible" title="文件预览" :width="width" :footer="null">
    <div :style="{ height }">
      <FilePreview :file-id="id" :preview-api="props.previewApi" />
    </div>
  </Modal>
</template>

<style scoped></style>
