<script setup lang="ts">
import { ref, watch } from 'vue';

const props = defineProps({
  fileId: {
    type: Number,
    default: null,
  },
  previewApi: {
    type: Function,
    required: true,
  },
});
const src = ref('');
const init = async (fileId: number) => {
  if (fileId) {
    src.value = await props.previewApi({ id: fileId });
  }
};
watch(
  () => props.fileId,
  async (fileId) => init(fileId),
  { immediate: true },
);
defineExpose({
  init,
});
</script>

<template>
  <iframe :src="src" class="h-full w-full"></iframe>
</template>

<style scoped></style>
