<script setup lang="ts">
import type { VxeGridProps, VxeGridPropTypes } from 'vxe-table';

import type { DriveFileInfo } from '@vben/types';

import { computed, ref, watch } from 'vue';

import { Button, Space, TypographyLink } from 'ant-design-vue';
import { VxeGrid } from 'vxe-table';

import { CloudDiskFilePicker, FilePreviewDialog } from '#/components';

interface AttachInfo {
  // 附件大小
  attachSize?: number;
  // 附件域名
  domainUrl?: string;
  // 附件拓展名
  extension?: string;
  // 哈希值
  fullHash?: string;
  // 主键
  id?: number;
  // 附件地址（慎重使用，文件名为oss中key名，需前端自行改名）
  link?: string;
  // 附件名称
  name?: string;
  // 附件原名
  originalName?: string;
  // 切片Hash
  sliceHash?: string;
  // 租户主键
  tenantId?: string;
  [property: string]: any;
}

const props = defineProps({
  businessId: { type: Number, default: null },
  businessType: { type: String, required: true },
  editMode: { type: Boolean, default: false },
  multiple: { type: Boolean, default: true },
  tableClassName: { type: String, default: '' },
  fileInfoApi: { type: Function, default: null },
  previewExternalApi: { type: Function, default: null },
  cloudDiskApiGroup: { type: Object, default: null },
  downloadApi: { type: Function, default: null },
  uploadApi: { type: Function, default: null },
  listApi: { type: Function, default: null },
});
const fileIdList = defineModel({ type: Array });
const fileList = ref<AttachInfo[]>([]);
const baseColumns: VxeGridPropTypes.Columns = [
  { field: 'originalName', title: '文件名称', slots: { default: 'originalName' } },
  { field: '', title: '上传人' },
  { field: 'createTime', title: '上传时间' },
  { field: 'action', title: '操作', slots: { default: 'action' } },
];
const editColumns: VxeGridPropTypes.Columns = [{ type: 'checkbox', width: '60px' }];
const columns = computed(() => {
  if (props.editMode) {
    return [...editColumns, ...baseColumns];
  }
  return [...baseColumns];
});
const gridOptions: VxeGridProps = {
  loading: false,
  toolbarConfig: {
    refresh: false,
    custom: false,
    zoom: false,
    slots: {
      tools: 'toolbarTools',
    },
  },
};
const CloudDiskFilePickerRef = ref();
const FilePreviewDialogRef = ref();
const GridRef = ref();
const init = async () => {
  if (props.businessId && props.businessType) {
    fileList.value = await props.listApi({ businessId: props.businessId, businessType: props.businessType });
    fileIdList.value = fileList.value.map((o: AttachInfo) => o.id);
  }
};
watch(
  () => props.businessId,
  () => {
    init();
  },
  { immediate: true },
);
watch(
  () => props.businessType,
  () => {
    init();
  },
);
const pick = async () => {
  const res = await CloudDiskFilePickerRef.value.pick({ multiple: props.multiple });
  res.file.forEach((o: DriveFileInfo) => {
    fileList.value.push({
      id: o.fileId || o.attachId,
      originalName: o.fileName || o.originalName,
    });
  });
  fileIdList.value = fileList.value.map((o: AttachInfo) => o.id);
};
const remove = () => {
  const records = GridRef.value.getCheckboxRecords();
  fileList.value = fileList.value.filter((o: AttachInfo) => !records.includes(o));
  fileIdList.value = fileList.value.map((o: AttachInfo) => o.id);
};
const handlePreview = (record: AttachInfo) => {
  FilePreviewDialogRef.value.init(record.id);
};
const download = async (item: AttachInfo) => {
  const res = await props.downloadApi({ id: item.id });
  window.open(res, '_blank');
};
</script>

<template>
  <div>
    <slot name="header"></slot>
    <VxeGrid ref="GridRef" :columns="columns" :data="fileList" :class="tableClassName" v-bind="gridOptions">
      <template #toolbarTools>
        <Space v-if="editMode">
          <Button type="primary" @click="pick">选择文件</Button>
          <Button type="primary" ghost danger @click="remove">删除</Button>
        </Space>
      </template>
      <template #originalName="{ row }">
        <TypographyLink @click="handlePreview(row)">{{ row.originalName }}</TypographyLink>
      </template>
      <template #action="{ row }">
        <TypographyLink @click="download(row)">下载</TypographyLink>
      </template>
    </VxeGrid>
    <CloudDiskFilePicker
      ref="CloudDiskFilePickerRef"
      v-if="editMode"
      :api-suite="cloudDiskApiGroup"
      :preview-external-api="previewExternalApi"
      :download-api="downloadApi"
      :upload-api="uploadApi"
    />
    <FilePreviewDialog ref="FilePreviewDialogRef" :preview-api="previewExternalApi" :download-api="downloadApi" />
  </div>
</template>

<style></style>
