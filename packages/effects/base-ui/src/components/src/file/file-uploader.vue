<script setup lang="ts">
import type { UploadChangeParam, UploadFile } from 'ant-design-vue/es/upload/interface';
import type { UploadRequestError, UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import type { ComponentPublicInstance } from 'vue';

import { computed, h, reactive, ref } from 'vue';

import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseOutlined,
  CloudUploadOutlined,
  ExclamationCircleOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  PauseOutlined,
} from '@ant-design/icons-vue';
import { Button, Upload } from 'ant-design-vue';
import SparkMD5 from 'spark-md5';

// 定义文件状态类型
type FileCustomStatus = 'done' | 'error' | 'paused' | 'success' | 'uploading' | 'waiting';

const props = defineProps({
  uploadApi: {
    type: Function,
    required: true,
  },
  preCheckApi: {
    type: Function,
    default: null,
  },
  checkApi: {
    type: Function,
    default: null,
  },
  folderId: {
    type: Number,
    default: 0,
  },
  // 是否在所有文件上传成功后自动关闭文件列表
  autoCloseOnSuccess: {
    type: Boolean,
    default: true,
  },
  // 自动关闭的延迟时间（毫秒）
  autoCloseDelay: {
    type: Number,
    default: 2000,
  },
});

const emit = defineEmits(['change', 'allSuccess', 'allCompleted']);
const UploadRef = ref<ComponentPublicInstance | null>(null);
const state = reactive({
  show: false,
});

// 文件列表，使用 UploadFile 类型
const fileList = ref<UploadFile[]>([]);

// 文件状态图标映射
const statusIconMap: Record<FileCustomStatus, { color: string; icon: any }> = {
  waiting: { icon: ClockCircleOutlined, color: '#909398' },
  uploading: { icon: LoadingOutlined, color: '#1890ff' },
  done: { icon: CheckCircleOutlined, color: '#52c41a' },
  success: { icon: CheckCircleOutlined, color: '#52c41a' },
  error: { icon: ExclamationCircleOutlined, color: '#ff4d4f' },
  paused: { icon: PauseOutlined, color: '#faad14' },
};

// 文件状态文本映射
const statusTextMap: Record<FileCustomStatus, string> = {
  waiting: '等待上传',
  uploading: '上传中',
  done: '上传成功',
  success: '上传成功',
  error: '上传失败',
  paused: '已暂停',
};

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) {
    return `${size} B`;
  } else if (size < 1024 * 1024) {
    return `${(size / 1024).toFixed(1)} KB`;
  } else if (size < 1024 * 1024 * 1024) {
    return `${(size / 1024 / 1024).toFixed(1)} MB`;
  } else {
    return `${(size / 1024 / 1024 / 1024).toFixed(1)} GB`;
  }
};

// 获取文件显示状态
const getFileDisplayStatus = (file: UploadFile & { customStatus?: FileCustomStatus }): FileCustomStatus => {
  if (file.customStatus) {
    return file.customStatus as FileCustomStatus;
  }
  return (file.status as FileCustomStatus) || 'waiting';
};

// 处理文件变化
const handleChange = (info: UploadChangeParam) => {
  fileList.value = info.fileList;
  // 向外部发送 change 事件
  emit('change', info);

  // 如果有文件，显示上传列表
  if (fileList.value.length > 0) {
    state.show = true;
  }

  // 延迟检查所有文件是否上传完成（等待状态更新）
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

// 检查是否所有文件都上传成功
const checkAllFilesSuccess = () => {
  if (fileList.value.length === 0) return;

  const allCompleted = fileList.value.every((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success' || status === 'error';
  });

  const allSuccess = fileList.value.every((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success';
  });

  // 如果所有文件都完成了上传（无论成功还是失败）
  if (allCompleted && fileList.value.length > 0) {
    const successFiles = fileList.value.filter((file) => {
      const status = getFileDisplayStatus(file);
      return status === 'done' || status === 'success';
    });

    const errorFiles = fileList.value.filter((file) => {
      const status = getFileDisplayStatus(file);
      return status === 'error';
    });

    // 触发所有文件完成事件
    emit('allCompleted', {
      files: fileList.value,
      total: fileList.value.length,
      successFiles,
      errorFiles,
      successCount: successFiles.length,
      errorCount: errorFiles.length,
    });

    // 如果全部成功，还要触发全部成功事件
    if (allSuccess) {
      emit('allSuccess', {
        files: successFiles,
        total: fileList.value.length,
        successCount: successFiles.length,
      });

      // 所有文件上传成功后，根据配置决定是否自动关闭文件列表显示
      if (props.autoCloseOnSuccess) {
        setTimeout(() => {
          state.show = false;
        }, props.autoCloseDelay);
      }
    }
  }
};

// 文件操作方法
const removeFile = (file: UploadFile) => {
  const index = fileList.value.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.value.splice(index, 1);
  }

  // 如果没有文件了，隐藏列表
  if (fileList.value.length === 0) {
    state.show = false;
  }

  // 检查是否所有文件完成
  setTimeout(() => {
    checkAllFilesSuccess();
  }, 100);
};

const clearAllFiles = () => {
  fileList.value = [];
  state.show = false;
};

// 手动关闭文件列表显示
const closeFileList = () => {
  state.show = false;
};

// 手动显示文件列表
const showFileList = () => {
  if (fileList.value.length > 0) {
    state.show = true;
  }
};

// 计算属性
const uploadingCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'uploading';
  }).length;
});

const successCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'done' || status === 'success';
  }).length;
});

const errorCount = computed(() => {
  return fileList.value.filter((file) => {
    const status = getFileDisplayStatus(file);
    return status === 'error';
  }).length;
});

const uploadFile = async ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  // 使用框架上传方法已实现 FormData 不需要重复转换
  const formData = { ...(data as object), [filename]: file };
  const checkFile = file as File;
  const sliceHash = await calculateMd5(checkFile);
  if (props.preCheckApi && props.checkApi) {
    const preCheckRes = await props.preCheckApi({ sliceHash });
    if (preCheckRes === 1) {
      try {
        const fullHash = await calculateMd5(checkFile, true);
        const checkRes = await props.checkApi({
          folderId: props.folderId,
          fullHash,
          newFileName: checkFile.name,
        });
        return onSuccess?.(checkRes.data);
      } catch (error) {
        console.error('Check error:', error);
      }
    }
  }
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      params: {
        folderId: props.folderId,
      },
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch((error: ProgressEvent<EventTarget> | UploadRequestError) => {
      if (onError) {
        onError(error);
      }
      console.error('Upload error:', error);
    });
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
/**
 * 计算文件（或文件切片）的 MD5 值
 * @param file 要计算 MD5 的文件对象
 * @param isFullFile 是否计算整个文件的 MD5。默认为 false，只计算前 256KB。
 * @returns 返回一个 Promise，成功时解析出 MD5 字符串，失败时返回错误。
 */
const calculateMd5 = async (file: File | null, isFullFile: boolean = false): Promise<string> => {
  if (!file) {
    throw new Error('File is not provided.');
  }

  const spark = new SparkMD5.ArrayBuffer();

  if (isFullFile) {
    // 如果是完整文件，使用切片方式处理
    const chunkSize = 2 * 1024 * 1024; // 2MB
    const chunks = Math.ceil(file.size / chunkSize);

    for (let i = 0; i < chunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      const arrayBuffer = await chunk.arrayBuffer();
      spark.append(arrayBuffer);
    }
  } else {
    // 只计算前 256KB
    const sliceSize = 256 * 1024;
    const chunk = file.slice(0, sliceSize);
    const arrayBuffer = await chunk.arrayBuffer();
    spark.append(arrayBuffer);
  }

  const md5 = spark.end();
  // console.log(`文件总大小: ${(file.size / 1024 / 1024).toFixed(2)} MB`);
  // console.log(`${isFullFile ? '完整文件' : '前256KB'} MD5: ${md5}`);
  return md5;
};

// 新增：用于拖拽上传的核心函数
const uploadFiles = (files: File[]) => {
  // 拖拽文件后，立即显示上传列表
  state.show = true;

  files.forEach((file) => {
    // 1. 为每个拖拽的 File 对象创建一个符合 a-upload 规范的 UploadFile 对象
    const uploadFileItem: UploadFile = {
      uid: `drag-drop-${Date.now()}-${Math.random().toString(36).slice(2)}`, // 生成唯一的 UID
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading', // 初始状态设为上传中
      percent: 0,
      originFileObj: file as any, // 存储原始 File 对象
    };

    // 2. 将这个对象添加到 fileList 中，UI 会自动更新
    fileList.value.push(uploadFileItem);

    // 3. 创建一个模拟的 UploadRequestOption 对象，用于手动调用 custom-request 函数
    const options: UploadRequestOption = {
      file,
      onSuccess: (response) => {
        const targetFile = fileList.value.find((f) => f.uid === uploadFileItem.uid);
        if (targetFile) {
          targetFile.status = 'done';
          // 手动触发一次 handleChange 来更新所有文件的状态
          handleChange({ file: targetFile, fileList: fileList.value, event: undefined });
        }
      },
      onError: (error) => {
        const targetFile = fileList.value.find((f) => f.uid === uploadFileItem.uid);
        if (targetFile) {
          targetFile.status = 'error';
          handleChange({ file: targetFile, fileList: fileList.value, event: undefined });
        }
        console.error(error);
      },
      onProgress: ({ percent }) => {
        const targetFile = fileList.value.find((f) => f.uid === uploadFileItem.uid);
        if (targetFile && typeof percent === 'number') {
          targetFile.percent = percent;
        }
      },
      // 其他 custom-request 可能需要的参数
      data: {},
      filename: 'file',
      headers: {},
      withCredentials: false,
    };

    // 4. 手动调用 custom-request (即 uploadFile 函数) 来启动该文件的上传流程
    uploadFile(options);
  });
};

interface ExposeMethods {
  fileList: typeof fileList;
  clearAllFiles: typeof clearAllFiles;
  closeFileList: typeof closeFileList;
  showFileList: typeof showFileList;
  checkAllFilesSuccess: typeof checkAllFilesSuccess;
  removeFile: typeof removeFile;
  uploadingCount: typeof uploadingCount;
  successCount: typeof successCount;
  errorCount: typeof errorCount;
  state: typeof state;
  // 新增：将 uploadFiles 方法也暴露出去
  uploadFiles: typeof uploadFiles;
}

defineExpose<ExposeMethods>({
  fileList,
  clearAllFiles,
  closeFileList,
  showFileList,
  checkAllFilesSuccess,
  removeFile,
  uploadingCount,
  successCount,
  errorCount,
  state,
  // 新增：将 uploadFiles 方法也暴露出去
  uploadFiles,
});
</script>

<template>
  <Upload
    ref="UploadRef"
    v-model:file-list="fileList"
    :custom-request="uploadFile"
    :show-upload-list="false"
    multiple
    v-bind="$attrs"
    @change="handleChange"
  >
    <Button :icon="h(CloudUploadOutlined)" type="primary"> 上传文件 </Button>
  </Upload>

  <div v-if="state.show" class="upload-list">
    <div class="upload-list-header">
      <div class="upload-list-title">
        <span>上传文件列表</span>
        <span class="file-count">({{ fileList.length }})</span>
      </div>
      <div class="upload-list-actions">
        <Button type="text" size="small" @click="clearAllFiles"> 清空列表 </Button>
        <Button type="text" size="small" @click="state.show = false">
          <CloseOutlined />
        </Button>
      </div>
    </div>

    <div class="upload-list-body">
      <div
        v-for="file in fileList"
        :key="file.uid"
        class="upload-file-item"
        :class="`status-${getFileDisplayStatus(file)}`"
      >
        <div
          class="file-progress-bg"
          :style="{ width: getFileDisplayStatus(file) === 'uploading' ? `${file.percent || 0}%` : '0%' }"
        ></div>

        <div class="file-info">
          <div class="file-name">
            <PaperClipOutlined class="file-icon" />
            <span class="name-text" :title="file.name">{{ file.name }}</span>
          </div>

          <div class="file-meta">
            <span class="file-size">{{ formatFileSize(file.size || 0) }}</span>
            <span class="file-status">
              <component
                :is="statusIconMap[getFileDisplayStatus(file)]?.icon || ClockCircleOutlined"
                :style="{ color: statusIconMap[getFileDisplayStatus(file)]?.color || '#909398' }"
                :spin="getFileDisplayStatus(file) === 'uploading'"
              />
              <span class="status-text">
                {{
                  getFileDisplayStatus(file) === 'uploading'
                    ? `${Math.round(file.percent || 0)}%`
                    : statusTextMap[getFileDisplayStatus(file)] || '等待上传'
                }}
              </span>
            </span>
          </div>
        </div>

        <div class="file-actions">
          <Button type="text" size="small" @click="removeFile(file)" title="移除" class="remove-btn">
            <CloseOutlined />
          </Button>
        </div>
      </div>

      <div v-if="fileList.length === 0" class="empty-state">
        <CloudUploadOutlined class="empty-icon" />
        <p>暂无上传文件</p>
      </div>
    </div>
  </div>
</template>

<style lang="less">
.upload-list {
  position: fixed;
  right: 32px;
  bottom: 32px;
  width: 600px;
  min-height: 300px;
  max-height: 500px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  z-index: 1000;

  .upload-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
    border-radius: 8px 8px 0 0;

    .upload-list-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;

      .file-count {
        color: #8c8c8c;
        font-weight: normal;
        margin-left: 4px;
      }
    }

    .upload-list-actions {
      display: flex;
      gap: 8px;
      align-items: center;

      .ant-btn {
        color: #8c8c8c;

        &:hover {
          color: #1890ff;
        }
      }
    }
  }

  .upload-list-body {
    flex: 1;
    overflow-y: auto;
    padding: 8px 0;

    .upload-file-item {
      position: relative;
      display: flex;
      align-items: center;
      padding: 12px 20px;
      border-bottom: 1px solid #f5f5f5;
      transition: all 0.2s ease;
      overflow: hidden;

      &:hover {
        background-color: #fafafa;
      }

      &:last-child {
        border-bottom: none;
      }

      // 进度条背景
      .file-progress-bg {
        position: absolute;
        left: 0;
        top: 0;
        height: 100%;
        background: linear-gradient(90deg, #e6f7ff 0%, #bae7ff 100%);
        transition: width 0.3s ease;
        z-index: 1;
      }

      // 状态样式
      &.status-success {
        .file-progress-bg {
          background: linear-gradient(90deg, #f6ffed 0%, #d9f7be 100%);
        }
      }

      &.status-error {
        .file-progress-bg {
          background: linear-gradient(90deg, #fff2f0 0%, #ffccc7 100%);
        }
      }

      &.status-paused {
        .file-progress-bg {
          background: linear-gradient(90deg, #fffbe6 0%, #fff1b8 100%);
        }
      }

      .file-info {
        flex: 1;
        position: relative;
        z-index: 2;
        min-width: 0;

        .file-name {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .file-icon {
            color: #8c8c8c;
            margin-right: 8px;
            flex-shrink: 0;
          }

          .name-text {
            font-size: 14px;
            color: #262626;
            font-weight: 500;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .file-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 12px;

          .file-size {
            color: #8c8c8c;
          }

          .file-status {
            display: flex;
            align-items: center;
            gap: 4px;

            .anticon {
              font-size: 12px;
            }

            .status-text {
              color: #595959;
            }
          }
        }
      }

      .file-actions {
        position: relative;
        z-index: 2;
        display: flex;
        gap: 4px;
        margin-left: 12px;

        .ant-btn {
          width: 24px;
          height: 24px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #8c8c8c;

          &:hover {
            color: #1890ff;
          }

          &.remove-btn:hover {
            color: #ff4d4f;
          }

          .anticon {
            font-size: 12px;
          }
        }
      }
    }

    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 20px;
      color: #bfbfbf;

      .empty-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 滚动条样式
.upload-list-body::-webkit-scrollbar {
  width: 6px;
}

.upload-list-body::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.upload-list-body::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
</style>
