.jv-dark {
  font-family: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>urier, monospace;
  font-size: 14px;
  color: #525252;
  white-space: nowrap;
  background: #112234;
  border-radius: var(--radius);

  .jv-ellipsis {
    display: inline-block;
    padding: 0 4px 2px;
    font-size: 0.9em;
    line-height: 0.9;
    vertical-align: 2px;
    color: #999;
    cursor: pointer;
    user-select: none;
    background-color: #eee;
    border-radius: 3px;
  }

  .jv-button {
    color: #49b3ff;
  }

  .jv-key {
    color: #ce6b41;
  }

  .jv-item {
    &.jv-array {
      color: #111;
    }

    &.jv-boolean {
      color: #fc1e70;
    }

    &.jv-function {
      color: #067bca;
    }

    &.jv-number {
      color: #fc1e70;
    }

    &.jv-number-float {
      color: #fc1e70;
    }

    &.jv-number-integer {
      color: #fc1e70;
    }

    &.jv-object {
      color: #eee;
    }

    &.jv-undefined {
      color: #e08331;
    }

    &.jv-string {
      color: #42b983;
      word-break: normal;
      white-space: normal;
    }
  }

  .jv-code {
    .jv-toggle {
      &::before {
        padding: 0 2px;
        border-radius: 2px;
      }

      &:hover {
        &::before {
          background: #eee;
        }
      }
    }
  }
}

.vscode-light {
  font-family: 'Fira Code', Consolas, monospace;
  font-size: 14px;
  color: #1f2328;
  background: var(--color-bg-3, #fff);
  border: 1px solid var(--color-border-2, #eee);
  border-radius: var(--radius);
}

.vscode-light .jv-tooltip.right {
  top: 5px;
}

.vscode-light .jv-key {
  font-weight: 600;
  color: #0550ae;
}

.vscode-light .jv-string {
  color: #0c5460;
}

.vscode-light .jv-number {
  color: #116329;
}

.vscode-light .jv-boolean {
  font-weight: 600;
  color: #c24038;
}

.vscode-light .jv-null {
  font-style: italic;
  color: #6f42c1;
}

.vscode-light .jv-undefined {
  color: #e36209;
}

.vscode-light .jv-function {
  color: #795548;
}

.vscode-light .jv-button {
  color: #0969da;
}

.vscode-light .jv-ellipsis {
  padding: 0 4px;
  color: #6a737d;
  background-color: #f6f8fa;
  border-radius: 4px;
}

.vscode-dark {
  font-family: 'Fira Code', Consolas, monospace;
  font-size: 14px;
  color: #d4d4d4;
  background: var(--color-bg-3, #17171a);
}

.vscode-dark .jv-key {
  font-weight: 600;
  color: #9cdcfe;
}

.vscode-dark .jv-string {
  color: #ce9178;
}

.vscode-dark .jv-number {
  color: #b5cea8;
}

.vscode-dark .jv-boolean {
  font-weight: 600;
  color: #569cd6;
}

.vscode-dark .jv-null {
  font-style: italic;
  color: #c586c0;
}

.vscode-dark .jv-undefined {
  color: #e08331;
}

.vscode-dark .jv-function {
  color: #dcdcaa;
}

.vscode-dark .jv-button {
  color: #4fc1ff;
}

.vscode-dark .jv-ellipsis {
  padding: 0 4px;
  color: #999;
  background-color: #333;
  border-radius: 4px;
}
