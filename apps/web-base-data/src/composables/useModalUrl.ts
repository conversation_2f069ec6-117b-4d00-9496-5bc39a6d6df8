import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

export interface ModalHandlers {
  [key: string]: (params: Record<string, any>) => void;
}

export interface UseModalUrlOptions {
  /**
   * 弹层处理函数映射
   * key: modal类型名称
   * value: 处理函数，接收URL参数对象
   */
  handlers: ModalHandlers;

  /**
   * 是否在处理完成后清除URL参数
   * 注意：清除URL参数可能导致组件重新渲染，从而影响弹层显示
   * @default false
   */
  clearParams?: boolean;

  /**
   * 需要从URL中提取的参数名列表
   * 如果不指定，则提取所有query参数
   */
  extractParams?: string[];

  /**
   * 参数转换函数
   * 可以对提取的参数进行类型转换或处理
   */
  transformParams?: (params: Record<string, any>) => Record<string, any>;
}

/**
 * URL参数自动打开弹层的组合式函数
 * @param options 配置选项
 * @returns 工具函数
 */
export function useModalUrl(options: UseModalUrlOptions) {
  const route = useRoute();
  const router = useRouter();

  const { handlers, clearParams = false, extractParams, transformParams } = options;

  /**
   * 处理URL参数
   */
  const handleUrlParams = async () => {
    const { modal, ...queryParams } = route.query;

    // 如果没有modal参数，直接返回
    if (!modal || typeof modal !== 'string') return;

    // 检查是否有对应的处理函数
    const handler = handlers[modal];
    if (!handler) {
      console.warn(`未找到modal类型 "${modal}" 对应的处理函数`);
      return;
    }

    try {
      // 提取需要的参数
      let params: Record<string, any> = {};

      if (extractParams) {
        // 只提取指定的参数
        extractParams.forEach((key) => {
          if (queryParams[key] !== undefined) {
            params[key] = queryParams[key];
          }
        });
      } else {
        // 提取所有参数（除了modal）
        params = { ...queryParams };
      }

      // 参数转换
      if (transformParams) {
        params = transformParams(params);
      }

      // 调用对应的处理函数
      handler(params);

      // 清除URL参数（可选）
      if (clearParams) {
        // 使用 history.replaceState 避免触发Vue Router的路由变化
        // 这样可以清除URL参数而不会导致组件重新渲染
        const url = new URL(window.location.href);
        url.search = '';
        window.history.replaceState({}, '', url.toString());
      }
    } catch (error) {
      console.error('处理URL参数时出错:', error);
    }
  };

  // 监听路由变化
  // watch(() => route.query, handleUrlParams);

  // 组件挂载时处理URL参数
  onMounted(() => {
    handleUrlParams();
  });

  /**
   * 生成带modal参数的URL
   * @param modal 弹层类型
   * @param params 其他参数
   * @param baseUrl 基础URL，默认使用当前路由路径
   * @returns 完整URL
   */
  const generateModalUrl = (modal: string, params: Record<string, any> = {}, baseUrl?: string): string => {
    const url = new URL(baseUrl || route.path, window.location.origin);
    url.searchParams.set('modal', modal);

    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.set(key, String(value));
      }
    });

    return url.toString();
  };

  /**
   * 编程式导航到modal页面
   * @param modal 弹层类型
   * @param params 其他参数
   */
  const navigateToModal = (modal: string, params: Record<string, any> = {}) => {
    router.push({
      path: route.path,
      query: { modal, ...params },
    });
  };

  /**
   * 在新标签页打开modal页面
   * @param modal 弹层类型
   * @param params 其他参数
   * @param baseUrl 基础URL
   */
  const openModalInNewTab = (modal: string, params: Record<string, any> = {}, baseUrl?: string) => {
    const url = generateModalUrl(modal, params, baseUrl);
    window.open(url, '_blank');
  };

  return {
    handleUrlParams,
    generateModalUrl,
    navigateToModal,
    openModalInNewTab,
  };
}

/**
 * 常用的参数转换函数
 */
export const paramTransformers = {
  /**
   * 将字符串ID转换为数字
   */
  stringToNumber: (params: Record<string, any>) => {
    const result = { ...params };
    if (result.id && typeof result.id === 'string') {
      result.id = Number(result.id);
    }
    return result;
  },

  /**
   * 将逗号分隔的字符串转换为数组
   */
  stringToArray: (key: string) => (params: Record<string, any>) => {
    const result = { ...params };
    if (result[key] && typeof result[key] === 'string') {
      result[key] = result[key].split(',');
    }
    return result;
  },

  /**
   * 组合多个转换函数
   */
  compose:
    (...transformers: Array<(params: Record<string, any>) => Record<string, any>>) =>
    (params: Record<string, any>) => {
      let result = params;
      for (const transformer of transformers) {
        result = transformer(result);
      }
      return result;
    },
};
