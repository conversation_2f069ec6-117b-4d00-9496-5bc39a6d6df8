# 企业准入页面 - URL参数自动打开弹层功能

## 功能说明

该页面使用 `useModalUrl` 组合式函数实现通过URL参数自动打开对应的弹层，方便从其他页面直接跳转到特定的编辑或详情页面。

## 支持的弹层类型

1. **edit** - 编辑弹层
2. **detail** - 详情弹层  
3. **audit** - 审核弹层

## URL参数格式

```
/company/access?modal={弹层类型}&参数1=值1&参数2=值2...
```

### 基础参数

- `modal`: 弹层类型，可选值：`edit`、`detail`、`audit`
- `id`: 记录ID（会自动转换为数字类型）

### 扩展参数

可以通过URL传递更多参数，这些参数会直接传递给弹层处理函数：

- `companyId`: 企业ID
- `accessRole`: 准入角色
- `companyName`: 企业名称
- `companyCode`: 统一社会信用代码
- `status`: 企业状态
- `reviewStatus`: 审批状态

## 使用示例

### 1. 基础用法

```
# 只传递ID
/company/access?modal=edit&id=123

# 传递多个参数
/company/access?modal=edit&id=123&companyId=456&accessRole=supplier&companyName=测试企业
```

### 2. 在Vue组件中使用

```typescript
// 导入工具函数
import { navigateToModal, generateModalUrl, openModalInNewTab } from '@/views/company/access/index.vue';

// 方式1: 编程式导航
navigateToModal('edit', { 
  id: 123, 
  companyId: 456, 
  accessRole: 'supplier' 
});

// 方式2: 生成URL
const url = generateModalUrl('edit', { 
  id: 123, 
  companyId: 456 
});

// 方式3: 在新标签页打开
openModalInNewTab('detail', { 
  id: 123, 
  companyName: '测试企业' 
});
```

### 3. 使用Vue Router

```typescript
import { useRouter } from 'vue-router';

const router = useRouter();

router.push({
  path: '/company/access',
  query: { 
    modal: 'edit', 
    id: '123',
    companyId: '456',
    accessRole: 'supplier'
  }
});
```

## 组合式函数 useModalUrl

### 基本用法

```typescript
import { useModalUrl, paramTransformers } from '@/composables/useModalUrl';

const { generateModalUrl, navigateToModal, openModalInNewTab } = useModalUrl({
  handlers: {
    edit: (params) => {
      // params 包含所有URL参数
      const data = { id: params.id, ...params };
      openEditModal(data);
    },
    detail: (params) => {
      const data = { id: params.id, ...params };
      openDetailModal(data);
    }
  },
  
  // 参数转换
  transformParams: paramTransformers.stringToNumber,
  
  // 指定需要提取的参数
  extractParams: ['id', 'companyId', 'accessRole']
});
```

### 配置选项

- `handlers`: 弹层处理函数映射
- `transformParams`: 参数转换函数
- `extractParams`: 需要提取的参数列表
- `clearParams`: 是否清除URL参数（默认false）

### 重要说明

**URL参数清除机制**：
- 默认情况下，URL参数不会被自动清除，以避免组件重新渲染导致弹层状态丢失
- 如果需要清除URL参数，可以设置 `clearParams: true`
- 清除操作使用 `window.history.replaceState` 而不是 Vue Router，避免触发组件重新渲染

## 在其他页面中复用

### 1. 创建处理函数

```typescript
// 在你的页面组件中
const { generateModalUrl, navigateToModal } = useModalUrl({
  handlers: {
    edit: (params) => openEditModal(params),
    detail: (params) => openDetailModal(params),
    create: (params) => openCreateModal(params)
  },
  transformParams: paramTransformers.stringToNumber
});
```

### 2. 导出工具函数

```typescript
// 导出供外部使用
export { generateModalUrl, navigateToModal, openModalInNewTab };
```

## 参数转换器

内置的参数转换器：

```typescript
import { paramTransformers } from '@/composables/useModalUrl';

// 字符串转数字
paramTransformers.stringToNumber

// 逗号分隔字符串转数组
paramTransformers.stringToArray('tags')

// 组合多个转换器
paramTransformers.compose(
  paramTransformers.stringToNumber,
  paramTransformers.stringToArray('tags')
)
```

## 优势

1. **可复用**: 组合式函数可以在任何页面中使用
2. **类型安全**: 完整的TypeScript支持
3. **灵活配置**: 支持自定义参数提取和转换
4. **无需API调用**: 所有数据通过URL参数传递
5. **简单集成**: 只需要提供弹层打开函数即可
