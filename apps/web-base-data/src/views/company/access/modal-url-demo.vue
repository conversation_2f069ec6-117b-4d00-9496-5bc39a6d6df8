<template>
  <div class="modal-url-demo">
    <h2>企业准入弹层URL演示 - 组合式函数版本</h2>
    
    <div class="demo-section">
      <h3>输入参数</h3>
      <a-form layout="vertical">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-form-item label="记录ID">
              <a-input-number v-model:value="params.id" :min="1" placeholder="请输入记录ID" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="弹层类型">
              <a-select v-model:value="modalType" style="width: 100%">
                <a-select-option value="edit">编辑</a-select-option>
                <a-select-option value="detail">详情</a-select-option>
                <a-select-option value="audit">审核</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="企业ID">
              <a-input-number v-model:value="params.companyId" placeholder="企业ID" style="width: 100%" />
            </a-form-item>
          </a-col>
          <a-col :span="6">
            <a-form-item label="准入角色">
              <a-input v-model:value="params.accessRole" placeholder="准入角色" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="企业名称">
              <a-input v-model:value="params.companyName" placeholder="企业名称" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="统一社会信用代码">
              <a-input v-model:value="params.companyCode" placeholder="统一社会信用代码" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="状态">
              <a-input v-model:value="params.status" placeholder="状态" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>

    <div class="demo-section">
      <h3>生成的URL</h3>
      <a-input 
        v-model:value="generatedUrl" 
        readonly 
        style="margin-bottom: 16px"
        placeholder="请先输入参数"
      />
      
      <div class="button-group">
        <a-button type="primary" @click="openInCurrentTab" :disabled="!isValidInput">
          在当前标签页打开
        </a-button>
        <a-button @click="openInNewTab" :disabled="!isValidInput">
          在新标签页打开
        </a-button>
        <a-button @click="copyUrl" :disabled="!isValidInput">
          复制URL
        </a-button>
      </div>
    </div>

    <div class="demo-section">
      <h3>快速测试链接</h3>
      <div class="quick-links">
        <a-space wrap>
          <a-button @click="quickTest('edit', { id: 1, companyName: '测试企业A' })">编辑企业A</a-button>
          <a-button @click="quickTest('detail', { id: 2, companyName: '测试企业B', status: 'ACTIVE' })">查看企业B详情</a-button>
          <a-button @click="quickTest('audit', { id: 3, companyName: '测试企业C', accessRole: 'supplier' })">审核企业C</a-button>
        </a-space>
      </div>
    </div>

    <div class="demo-section">
      <h3>代码示例</h3>
      <a-tabs>
        <a-tab-pane key="composable" tab="组合式函数">
          <pre><code>{{ composableCode }}</code></pre>
        </a-tab-pane>
        <a-tab-pane key="router" tab="Vue Router">
          <pre><code>{{ routerCode }}</code></pre>
        </a-tab-pane>
        <a-tab-pane key="url" tab="直接URL">
          <pre><code>{{ urlCode }}</code></pre>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { useModalUrl, paramTransformers } from '#/composables/useModalUrl';
import { message } from 'ant-design-vue';

const modalType = ref<'edit' | 'detail' | 'audit'>('edit');
const params = reactive({
  id: undefined as number | undefined,
  companyId: undefined as number | undefined,
  accessRole: '',
  companyName: '',
  companyCode: '',
  status: ''
});

// 使用组合式函数
const { generateModalUrl, navigateToModal, openModalInNewTab } = useModalUrl({
  handlers: {
    edit: (urlParams) => {
      console.log('打开编辑弹层，参数:', urlParams);
      message.info(`打开编辑弹层: ID=${urlParams.id}, 企业名称=${urlParams.companyName || '未指定'}`);
    },
    detail: (urlParams) => {
      console.log('打开详情弹层，参数:', urlParams);
      message.info(`打开详情弹层: ID=${urlParams.id}, 状态=${urlParams.status || '未指定'}`);
    },
    audit: (urlParams) => {
      console.log('打开审核弹层，参数:', urlParams);
      message.info(`打开审核弹层: ID=${urlParams.id}, 角色=${urlParams.accessRole || '未指定'}`);
    }
  },
  transformParams: paramTransformers.stringToNumber,
  extractParams: ['id', 'companyId', 'accessRole', 'companyName', 'companyCode', 'status']
});

// 生成URL
const generatedUrl = computed(() => {
  if (!modalType.value || !params.id) return '';
  
  // 过滤掉空值
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined && value !== '')
  );
  
  return generateModalUrl(modalType.value, filteredParams);
});

// 验证输入
const isValidInput = computed(() => {
  return modalType.value && params.id && params.id > 0;
});

// 在当前标签页打开
const openInCurrentTab = () => {
  if (!isValidInput.value) return;
  
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined && value !== '')
  );
  
  navigateToModal(modalType.value, filteredParams);
};

// 在新标签页打开
const openInNewTab = () => {
  if (!isValidInput.value) return;
  
  const filteredParams = Object.fromEntries(
    Object.entries(params).filter(([_, value]) => value !== undefined && value !== '')
  );
  
  openModalInNewTab(modalType.value, filteredParams);
};

// 复制URL
const copyUrl = async () => {
  if (!isValidInput.value) return;
  
  try {
    await navigator.clipboard.writeText(generatedUrl.value);
    message.success('URL已复制到剪贴板');
  } catch (error) {
    message.error('复制失败，请手动复制');
  }
};

// 快速测试
const quickTest = (modal: 'edit' | 'detail' | 'audit', testParams: Record<string, any>) => {
  navigateToModal(modal, testParams);
};

// 代码示例
const composableCode = computed(() => `
// 使用组合式函数
import { useModalUrl, paramTransformers } from '@/composables/useModalUrl';

const { generateModalUrl, navigateToModal, openModalInNewTab } = useModalUrl({
  handlers: {
    ${modalType.value}: (params) => {
      // params: ${JSON.stringify(params, null, 2)}
      open${modalType.value.charAt(0).toUpperCase() + modalType.value.slice(1)}Modal(params);
    }
  },
  transformParams: paramTransformers.stringToNumber,
  extractParams: ['id', 'companyId', 'accessRole', 'companyName']
});

// 调用
navigateToModal('${modalType.value}', ${JSON.stringify(params, null, 2)});
`);

const routerCode = computed(() => `
// 使用Vue Router
import { useRouter } from 'vue-router';

const router = useRouter();

router.push({
  path: '/company/access',
  query: ${JSON.stringify({ modal: modalType.value, ...params }, null, 2)}
});
`);

const urlCode = computed(() => `
<!-- 直接使用URL -->
${generatedUrl.value || '请先输入参数'}
`);
</script>

<style scoped>
.modal-url-demo {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.demo-section h3 {
  margin-top: 0;
  margin-bottom: 16px;
  color: #1890ff;
}

.button-group {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.quick-links {
  margin-top: 8px;
}

pre {
  background-color: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0;
  max-height: 400px;
  overflow-y: auto;
}

code {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}
</style>
