<script setup lang="ts">
import type { LogOperationInfo } from '#/api';

import { computed, ref } from 'vue';

import { <PERSON>son<PERSON>iewer } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getOperationLogDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';

const logDetail = ref<LogOperationInfo>({ id: 0 });
const init = async (data: { id: number }) => {
  logDetail.value = await getOperationLogDetailApi({ id: data.id });
};
const [registerPopup] = usePopupInner(init);
const detailSchema = [
  { fieldName: 'createTime', label: '操作时间' },
  { fieldName: 'userName', label: '操作用户' },
  { fieldName: 'title', label: '操作内容' },
  { fieldName: 'module', label: '所属模块' },
  { fieldName: 'requestUri', label: '请求接口' },
  { fieldName: 'method', label: '请求方式' },
  { fieldName: 'methodClass', label: '方法类' },
  { fieldName: 'methodName', label: '方法名' },
  { fieldName: 'remoteIp', label: '操作Ip' },
  { fieldName: 'address', label: '操作地点' },
  { fieldName: 'device', label: '终端设备' },
  { fieldName: 'os', label: '终端系统' },
  { fieldName: 'client', label: '浏览器' },
  { fieldName: 'userAgent', label: '用户代理' },
  { fieldName: 'env', label: '软件环境' },
  { fieldName: 'serverIp', label: '服务Ip' },
  { fieldName: 'serverHost', label: '服务Host' },
  { fieldName: 'serviceId', label: '服务Id' },
  { fieldName: 'time', label: '耗时', formatter: (value: number) => `${value ?? ''}ms` },
];
const params = computed(() => {
  try {
    return JSON.parse(logDetail.value.params ?? '{}');
  } catch {
    return logDetail.value.params;
  }
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="系统日志详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <DynamicDescriptions :data="logDetail" :schema="detailSchema" v-bind="DESCRIPTIONS_PROP" />
      <BasicCaption content="请求数据" class="my-4" />
      <JsonViewer v-if="logDetail.params" :value="params" copyable />
      <span v-else>无</span>
    </div>
  </BasicPopup>
</template>

<style></style>
