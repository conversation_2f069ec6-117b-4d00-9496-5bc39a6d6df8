<script setup lang="ts">
import type { LogErrorInfo } from '#/api';

import { computed, ref } from 'vue';

import { <PERSON>son<PERSON>iewer } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getErrorLogDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';

const logDetail = ref<LogErrorInfo>({ id: 0 });
const init = async (data: { id: number }) => {
  logDetail.value = await getErrorLogDetailApi({ id: data.id });
};
const [registerPopup] = usePopupInner(init);
const detailSchema = [
  { fieldName: 'createTime', label: '请求时间' },
  { fieldName: 'userName', label: '请求用户' },
  { fieldName: 'requestUri', label: '请求接口' },
  { fieldName: 'method', label: '请求方式' },
  { fieldName: 'methodClass', label: '方法类' },
  { fieldName: 'methodName', label: '方法名' },
  { fieldName: 'remoteIp', label: '请求Ip' },
  { fieldName: 'address', label: '请求地点' },
  { fieldName: 'device', label: '终端设备' },
  { fieldName: 'os', label: '终端系统' },
  { fieldName: 'client', label: '浏览器' },
  { fieldName: 'userAgent', label: '用户代理' },
  { fieldName: 'env', label: '软件环境' },
  { fieldName: 'serverIp', label: '服务Ip' },
  { fieldName: 'serverHost', label: '服务Host' },
  { fieldName: 'serviceId', label: '服务Id' },
  { fieldName: 'exceptionName', label: '异常名' },
  { fieldName: 'message', label: '异常消息' },
  { fieldName: 'fileName', label: '文件名' },
  { fieldName: 'lineNumber', label: '代码行数' },
];
const params = computed(() => {
  try {
    return JSON.parse(logDetail.value.params ?? '{}');
  } catch {
    return logDetail.value.params;
  }
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="系统日志详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <DynamicDescriptions :data="logDetail" :schema="detailSchema" v-bind="DESCRIPTIONS_PROP" />
      <BasicCaption content="请求数据" class="my-4" />
      <JsonViewer v-if="logDetail.params" :value="params" copyable />
      <span v-else>无</span>
      <BasicCaption content="堆栈信息" class="my-4" />
      <JsonViewer v-if="logDetail.stackTrace" :value="logDetail.stackTrace" copyable />
      <span v-else>无</span>
    </div>
  </BasicPopup>
</template>

<style></style>
