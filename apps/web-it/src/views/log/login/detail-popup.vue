<script setup lang="ts">
import type { LogLoginInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getLoginLogDetailApi } from '#/api';
import DynamicDescriptions from '#/components/dynamic-descriptions.vue';

const logDetail = ref<LogLoginInfo>({ id: 0, ip: '' });
const init = async (data: { id: number }) => {
  logDetail.value = await getLoginLogDetailApi({ id: data.id });
};
const [registerPopup] = usePopupInner(init);
const detailSchema = [
  { fieldName: 'loginTime', label: '登录时间' },
  { fieldName: 'userName', label: '登录用户' },
  { fieldName: 'ip', label: '登录Ip' },
  { fieldName: 'address', label: '登录地点' },
  { fieldName: 'platForm', label: '登录平台', layoutType: 'tag', dictCode: 'LOGIN_PLATFORM' },
  { fieldName: 'loginType', label: '登录类型', layoutType: 'tag', dictCode: 'LOGIN_TYPE' },
  { fieldName: 'device', label: '终端设备' },
  { fieldName: 'os', label: '终端系统' },
  { fieldName: 'client', label: '浏览器' },
  { fieldName: 'userAgent', label: '用户代理' },
];
</script>

<template>
  <BasicPopup v-bind="$attrs" title="登录日志详情" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME" class="mt-4">
      <DynamicDescriptions :data="logDetail" :schema="detailSchema" v-bind="DESCRIPTIONS_PROP" />
    </div>
  </BasicPopup>
</template>

<style></style>
